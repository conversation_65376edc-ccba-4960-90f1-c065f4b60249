import os
import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import argparse
import matplotlib
import h5py
import random
from material_distribution_model import EMPointCloudDataset, MaterialDistributionModel, MATERIAL_PROPERTIES
from train_material_model import train_model, get_material_label
from robot_material_recognition import RobotMaterialRecognition

# 设置中文字体
try:
    # 尝试使用微软雅黑
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'NSimSun', 'FangSong', 'KaiTi']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    matplotlib.rcParams['font.family'] = 'sans-serif'
    print("已设置中文字体")
except:
    print("警告: 无法设置中文字体，图表中的中文可能无法正确显示")

def load_real_data_from_h5(data_dir, object_type=None):
    """
    从h5文件加载真实数据

    Args:
        data_dir: 数据目录
        object_type: 对象类型，如果为None则随机选择

    Returns:
        points: 点云坐标
        epsr_values: 介电常数值
        true_labels: 根据介电常数值确定的材料标签
        obj_type: 对象类型
    """
    # 获取所有h5文件
    h5_files = [f for f in os.listdir(data_dir) if f.endswith('_with_em.h5')]

    if not h5_files:
        raise ValueError(f"在 {data_dir} 中没有找到h5文件")

    # 如果没有指定对象类型，随机选择一个
    if object_type is None:
        h5_file = random.choice(h5_files)
        object_type = h5_file.split('_with_em.h5')[0]
    else:
        h5_file = f"{object_type}_with_em.h5"
        if h5_file not in h5_files:
            raise ValueError(f"找不到对象类型 {object_type} 的h5文件")

    file_path = os.path.join(data_dir, h5_file)
    print(f"从文件加载数据: {file_path}")

    # 从h5文件加载数据
    with h5py.File(file_path, 'r') as f:
        # 检查文件中的数据集
        modes = ['train', 'test']
        available_samples = []

        for mode in modes:
            if mode in f:
                for sample_key in f[mode].keys():
                    available_samples.append((mode, sample_key))

        if not available_samples:
            raise ValueError(f"在文件 {file_path} 中没有找到有效的样本")

        # 随机选择一个样本
        mode, sample_key = random.choice(available_samples)
        print(f"选择样本: {mode}/{sample_key}")

        # 读取点云和EM数据
        data = f[f'{mode}/{sample_key}/pcs_epsr'][:]

        # 确保数据不为空且形状正确
        if data.size == 0 or data.shape[1] != 4:
            raise ValueError(f"样本 {sample_key} 的数据为空或形状不正确")

        # 分离点云坐标和介电常数
        points = data[:, :3]
        epsr_values = data[:, 3]

        # 根据介电常数值确定材料标签
        true_labels = np.array([get_material_label(epsr) for epsr in epsr_values])

        print(f"加载了 {len(points)} 个点")

        return points, epsr_values, true_labels, object_type

def visualize_enhanced_material_distribution(points, material_labels, title="材料分布", epsr_values=None):
    """
    增强版材料分布可视化，提供更详细的材料信息

    Args:
        points: 点云坐标，形状为(N, 3)
        material_labels: 材料标签，形状为(N,)
        title: 图表标题
        epsr_values: 介电常数值，形状为(N,)，可选
    """
    # 创建图表
    fig = plt.figure(figsize=(14, 12))
    ax = fig.add_subplot(111, projection='3d')

    # 设置背景色为白色，增强对比度
    ax.set_facecolor('white')
    fig.set_facecolor('white')

    # 获取颜色 - 使用更鲜明的颜色
    material_colors = {
        0: '#8B4513',  # 木材 - 深棕色
        1: '#FF4500',  # 塑料 - 鲜红色
        2: '#4682B4',  # 金属 - 钢蓝色
        3: '#2F4F4F',  # 未知 - 深青色
    }
    colors = [material_colors[label] for label in material_labels]

    # 绘制点云 - 增大点的大小和不透明度
    scatter = ax.scatter(points[:, 0], points[:, 1], points[:, 2], c=colors, s=15, alpha=0.8, edgecolors='none')

    # 计算每种材料的点数
    material_counts = {}
    for material, props in MATERIAL_PROPERTIES.items():
        count = np.sum(material_labels == props['label'])
        material_counts[material] = count

    # 添加图例 - 放在图表内部
    legend_elements = []
    for material, props in MATERIAL_PROPERTIES.items():
        if material_counts[material] > 0:  # 只显示存在的材料
            legend_elements.append(plt.Line2D([0], [0], marker='o', color='w',
                                            markerfacecolor=props['color'],
                                            label=props['name'],
                                            markersize=12))

    ax.legend(handles=legend_elements, loc='upper right', fontsize=14,
              framealpha=0.9, edgecolor='gray')

    # 设置标题和轴标签
    plt.suptitle(f"材料分布统计 (总点数: {len(material_labels)})",
                fontsize=20, fontweight='bold', y=0.98)
    plt.title(title, fontsize=18, pad=10)

    ax.set_xlabel('X轴', fontsize=16)
    ax.set_ylabel('Y轴', fontsize=16)
    ax.set_zlabel('Z轴', fontsize=16)

    # 添加网格线
    ax.grid(True, linestyle='--', alpha=0.3)

    # 在右侧添加材料统计信息 - 先显示介电常数，再显示材料名
    for i, (material, count) in enumerate(material_counts.items()):
        if count > 0:  # 只显示存在的材料
            percentage = (count / len(material_labels)) * 100
            props = MATERIAL_PROPERTIES[material]

            # 计算该材料的平均介电常数
            mask = material_labels == props['label']
            if epsr_values is not None and np.sum(mask) > 0:
                avg_epsr = np.mean(epsr_values[mask])
                epsr_range = props.get('epsr_range', 'N/A')
                # 先显示介电常数，再显示材料名
                text = f"εᵣ={avg_epsr:.2f} ({epsr_range}) - {props['name']}: {count} 点 ({percentage:.1f}%)"
            else:
                # 如果没有介电常数数据，只显示材料名
                text = f"{props['name']}: {count} 点 ({percentage:.1f}%)"

            # 创建更好看的标签框
            ax.text2D(1.02, 0.95 - i*0.07,
                    text,
                    transform=ax.transAxes,
                    fontsize=12,
                    bbox=dict(facecolor='white', alpha=0.9, edgecolor=props['color'],
                             boxstyle='round,pad=0.5'))

    # 如果提供了介电常数值，添加介电常数分布信息
    if epsr_values is not None:
        # 计算每种材料的平均介电常数
        material_epsr = {}
        for material, props in MATERIAL_PROPERTIES.items():
            if material_counts[material] > 0:
                mask = material_labels == props['label']
                material_epsr[material] = np.mean(epsr_values[mask])

        # 添加介电常数信息
        ax.text2D(1.02, 0.2,
                "介电常数分布:",
                transform=ax.transAxes,
                fontsize=14,
                fontweight='bold',
                bbox=dict(facecolor='white', alpha=0.9, edgecolor='black',
                         boxstyle='round,pad=0.5'))

        for i, (material, epsr) in enumerate(material_epsr.items()):
            if material_counts[material] > 0:
                props = MATERIAL_PROPERTIES[material]
                epsr_range = props.get('epsr_range', 'N/A')
                ax.text2D(1.02, 0.15 - i*0.07,
                        f"εᵣ={epsr:.2f} ({epsr_range}) - {props['name']}",
                        transform=ax.transAxes,
                        fontsize=12,
                        bbox=dict(facecolor='white', alpha=0.9, edgecolor=props['color'],
                                 boxstyle='round,pad=0.5'))

    # 调整视角 - 使用更好的视角
    ax.view_init(elev=25, azim=30)

    # 设置轴的范围，使图形更紧凑
    x_min, x_max = np.min(points[:, 0]), np.max(points[:, 0])
    y_min, y_max = np.min(points[:, 1]), np.max(points[:, 1])
    z_min, z_max = np.min(points[:, 2]), np.max(points[:, 2])

    # 添加一点边距
    margin = 0.1
    ax.set_xlim(x_min - margin, x_max + margin)
    ax.set_ylim(y_min - margin, y_max + margin)
    ax.set_zlim(z_min - margin, z_max + margin)

    # 调整图表布局
    plt.tight_layout(rect=[0, 0, 0.85, 0.95])  # 为右侧的文本留出空间

    # 保存图像
    filename = f"{title.replace(' ', '_').replace('(', '').replace(')', '')}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"已保存图像到 {filename}")

    plt.show()

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='增强版材料分布识别系统演示')
    parser.add_argument('--data_dir', type=str, default='data/ModelNet40_EM',
                        help='数据目录')
    parser.add_argument('--model_path', type=str, default='material_model_final.pth',
                        help='模型路径')
    parser.add_argument('--train', action='store_true',
                        help='是否训练模型')
    parser.add_argument('--object_type', type=str, default=None,
                        help='对象类型，如果不指定则随机选择')
    args = parser.parse_args()

    print("=" * 50)
    print("增强版材料分布识别系统演示")
    print("=" * 50)

    # 打印材料属性
    print("\n支持的材料类型:")
    for material, props in MATERIAL_PROPERTIES.items():
        if material != 'unknown':
            print(f"  {props['name']}: 介电常数范围 {props['epsr_range']}")

    # 检查数据目录是否存在
    if not os.path.exists(args.data_dir):
        print(f"错误: 数据目录 {args.data_dir} 不存在")
        return

    # 训练模型或检查模型文件是否存在
    if args.train or not os.path.exists(args.model_path):
        if not os.path.exists(args.model_path):
            print(f"模型文件 {args.model_path} 不存在，将训练新模型")

        print("\n开始训练材料分布模型...")
        _, _, _ = train_model(args.data_dir, batch_size=4, epochs=30)
        print(f"模型已保存到 {args.model_path}")

    # 初始化机械臂材料识别系统
    robot_recognition = RobotMaterialRecognition(args.model_path)

    # 从真实数据集加载数据
    print("\n从真实数据集加载数据...")
    try:
        # 如果指定了对象类型，尝试加载
        if args.object_type:
            points, epsr_values, true_labels, obj_type = load_real_data_from_h5(
                args.data_dir, args.object_type
            )
        else:
            # 尝试加载已知的对象类型
            known_types = ['bed', 'chair', 'desk', 'bottle']
            for obj_type in known_types:
                try:
                    points, epsr_values, true_labels, obj_type = load_real_data_from_h5(
                        args.data_dir, obj_type
                    )
                    break
                except Exception as e:
                    print(f"尝试加载 {obj_type} 时出错: {str(e)}")
                    continue
            else:
                raise ValueError("无法加载任何已知对象类型的数据")

        # 处理点云
        print("处理点云数据...")
        pred_labels, _ = robot_recognition.process_point_cloud(points, epsr_values)

        # 可视化真实材料分布
        visualize_enhanced_material_distribution(
            points,
            true_labels,
            title=f"{obj_type} - 材料分布(真实)",
            epsr_values=epsr_values
        )

        # 可视化预测材料分布
        visualize_enhanced_material_distribution(
            points,
            pred_labels,
            title=f"{obj_type} - 材料分布(预测)",
            epsr_values=epsr_values
        )

        # 计算准确率
        from sklearn.metrics import accuracy_score
        accuracy = accuracy_score(true_labels, pred_labels)
        print(f"\n材料识别准确率: {accuracy:.4f}")

        # 处理所有对象类型
        print("\n处理所有对象类型...")
        object_types_to_process = ['bed', 'chair', 'desk', 'bottle', 'cup']

        results = {}

        for obj_type in object_types_to_process:
            print(f"\n处理对象类型: {obj_type}")

            try:
                points, epsr_values, true_labels, _ = load_real_data_from_h5(
                    args.data_dir, obj_type
                )

                # 处理点云
                print("处理点云数据...")
                pred_labels, _ = robot_recognition.process_point_cloud(points, epsr_values)

                # 可视化真实材料分布
                visualize_enhanced_material_distribution(
                    points,
                    true_labels,
                    title=f"{obj_type} - 材料分布(真实)",
                    epsr_values=epsr_values
                )

                # 可视化预测材料分布
                visualize_enhanced_material_distribution(
                    points,
                    pred_labels,
                    title=f"{obj_type} - 材料分布(预测)",
                    epsr_values=epsr_values
                )

                # 计算准确率
                accuracy = accuracy_score(true_labels, pred_labels)
                print(f"材料识别准确率: {accuracy:.4f}")

                # 保存结果
                results[obj_type] = accuracy

            except Exception as e:
                print(f"处理 {obj_type} 时出错: {str(e)}")
                continue

        # 打印所有结果
        print("\n所有对象类型的材料识别准确率:")
        for obj_type, accuracy in results.items():
            print(f"{obj_type}: {accuracy:.4f}")

    except Exception as e:
        print(f"处理数据时出错: {str(e)}")

    print("\n演示完成")

if __name__ == "__main__":
    main()
