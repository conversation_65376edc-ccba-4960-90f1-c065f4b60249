import os
import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import argparse
import matplotlib
from material_distribution_model import MATERIAL_PROPERTIES
from train_material_model import train_model, visualize_results, plot_confusion_matrix
from robot_material_recognition import RobotMaterialRecognition, simulate_robot_scan

# 设置中文字体
try:
    # 尝试使用微软雅黑
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'NSimSun', 'FangSong', 'KaiTi']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    matplotlib.rcParams['font.family'] = 'sans-serif'
    print("已设置中文字体")
except:
    print("警告: 无法设置中文字体，图表中的中文可能无法正确显示")

def enhance_visualization(ax, title):
    """
    增强可视化效果的辅助函数
    """
    # 设置背景色
    ax.set_facecolor('#F5F5F5')

    # 添加网格线
    ax.grid(True, linestyle='--', alpha=0.3)

    # 设置标题和轴标签 - 增大字体
    ax.set_title(title, fontsize=18, fontweight='bold')
    ax.set_xlabel('X轴', fontsize=14)
    ax.set_ylabel('Y轴', fontsize=14)
    ax.set_zlabel('Z轴', fontsize=14)

def parse_args():
    parser = argparse.ArgumentParser(description='材料分布识别系统演示')
    parser.add_argument('--mode', type=str, default='demo', choices=['train', 'test', 'demo'],
                        help='运行模式: train (训练模型), test (测试模型), demo (演示)')
    parser.add_argument('--data_dir', type=str, default='data/ModelNet40_EM',
                        help='数据目录')
    parser.add_argument('--model_path', type=str, default='material_model_final.pth',
                        help='模型路径')
    parser.add_argument('--epochs', type=int, default=30,
                        help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=4,
                        help='批次大小')
    parser.add_argument('--visualize', action='store_true',
                        help='是否可视化结果')
    return parser.parse_args()

def train_mode(args):
    """
    训练模式
    """
    print(f"开始训练模型，数据目录: {args.data_dir}")
    model, train_losses, val_accuracies = train_model(
        args.data_dir,
        batch_size=args.batch_size,
        epochs=args.epochs
    )

    # 绘制训练曲线
    plt.figure(figsize=(12, 5))

    # 训练损失
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, linewidth=2)
    plt.title('训练损失', fontsize=14, fontweight='bold')
    plt.xlabel('Epoch', fontsize=12)
    plt.ylabel('Loss', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.3)

    # 验证准确率
    plt.subplot(1, 2, 2)
    plt.plot(val_accuracies, linewidth=2)
    plt.title('验证准确率', fontsize=14, fontweight='bold')
    plt.xlabel('Epoch', fontsize=12)
    plt.ylabel('Accuracy', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.3)

    plt.tight_layout()
    plt.savefig("training_curves.png", dpi=300, bbox_inches='tight')
    plt.show()

    if args.visualize:
        # 可视化预测结果
        print("可视化预测结果...")
        visualize_results(model, args.data_dir)

        # 绘制混淆矩阵
        print("绘制混淆矩阵...")
        plot_confusion_matrix(model, args.data_dir)

    print(f"模型已保存到 {args.model_path}")

def test_mode(args):
    """
    测试模式 - 使用真实数据集而非模拟数据
    """
    # 检查模型文件是否存在
    if not os.path.exists(args.model_path):
        print(f"错误: 模型文件 {args.model_path} 不存在")
        print("请先运行训练模式 (--mode train) 训练模型")
        return

    # 初始化机械臂材料识别系统
    robot_recognition = RobotMaterialRecognition(args.model_path)

    # 加载真实数据集
    print(f"从数据集 {args.data_dir} 加载测试数据...")
    from material_distribution_model import EMPointCloudDataset
    from torch.utils.data import DataLoader

    # 创建测试数据集
    test_dataset = EMPointCloudDataset(args.data_dir, mode='test')
    print(f"加载了 {len(test_dataset)} 个test样本")

    # 如果测试集为空，则使用训练集
    if len(test_dataset) == 0:
        print("测试集为空，使用训练集...")
        test_dataset = EMPointCloudDataset(args.data_dir, mode='train')
        print(f"加载了 {len(test_dataset)} 个train样本")

    if len(test_dataset) == 0:
        print("错误: 数据集为空")
        return

    # 随机选择一个样本
    import random
    sample_idx = random.randint(0, len(test_dataset) - 1)
    sample = test_dataset[sample_idx]

    # 根据返回值数量解析样本
    if len(sample) == 3:
        points, epsr_values, obj_type = sample
        true_labels = None
    else:
        points, epsr_values, true_labels, obj_type = sample

    print(f"选择的样本: {obj_type}, 点数: {points.shape[0]}")

    # 确保数据是NumPy数组
    points_np = points
    epsr_np = epsr_values

    if isinstance(points, torch.Tensor):
        points_np = points.detach().cpu().numpy()

    if isinstance(epsr_values, torch.Tensor):
        epsr_np = epsr_values.detach().cpu().numpy()

    # 处理点云
    print("处理点云数据...")
    material_labels, confidence = robot_recognition.process_point_cloud(points_np, epsr_np)

    # 分割点云
    segmented_points = robot_recognition.segment_by_material(points_np, material_labels)

    # 保存分割后的点云
    robot_recognition.save_segmented_point_cloud(segmented_points)

    # 可视化材料分布
    robot_recognition.visualize_material_distribution(
        points_np,
        material_labels,
        title=f"真实数据 - {obj_type} 的材料分布",
        epsr_values=epsr_np
    )

    # 如果有真实标签，计算准确率
    if true_labels is not None:
        from sklearn.metrics import accuracy_score
        true_labels_np = true_labels
        if isinstance(true_labels, torch.Tensor):
            true_labels_np = true_labels.detach().cpu().numpy()

        accuracy = accuracy_score(true_labels_np.flatten(), material_labels)
        print(f"材料识别准确率: {accuracy:.4f}")

def demo_mode(args):
    """
    演示模式 - 简化版，显示结果后直接退出
    """
    import os

    print("=" * 50)
    print("材料分布识别系统演示")
    print("=" * 50)

    # 打印材料属性
    print("\n支持的材料类型:")
    for material, props in MATERIAL_PROPERTIES.items():
        if material != 'unknown':
            print(f"  {props['name']}: 介电常数范围 {props['epsr_range']}")

    # 检查模型文件是否存在
    if not os.path.exists(args.model_path):
        print(f"\n模型文件 {args.model_path} 不存在，需要先训练模型")
        train_choice = input("是否现在训练模型? (y/n): ")
        if train_choice.lower() == 'y':
            train_mode(args)
        else:
            print("退出演示")
            return

    # 初始化机械臂材料识别系统
    robot_recognition = RobotMaterialRecognition(args.model_path)

    # 加载数据集
    from material_distribution_model import EMPointCloudDataset

    # 创建测试数据集
    test_dataset = EMPointCloudDataset(args.data_dir, mode='test')
    print(f"加载了 {len(test_dataset)} 个test样本")

    # 如果测试集为空，则使用训练集
    if len(test_dataset) == 0:
        print("测试集为空，使用训练集...")
        test_dataset = EMPointCloudDataset(args.data_dir, mode='train')
        print(f"加载了 {len(test_dataset)} 个train样本")

    if len(test_dataset) == 0:
        print(f"错误: 数据集 {args.data_dir} 为空")
        return

    # 检查数据目录中的所有可用对象类型
    print("\n检查数据目录中的所有可用对象类型...")
    available_objects = []

    # 扫描数据目录中的所有h5文件
    for filename in os.listdir(args.data_dir):
        if filename.endswith('_with_em.h5'):
            obj_type = filename.split('_with_em.h5')[0]
            available_objects.append(obj_type)

    if not available_objects:
        print("警告: 在数据目录中没有找到任何对象类型")
        # 尝试从数据集中获取对象类型
        obj_types = set()
        for i in range(len(test_dataset)):
            sample = test_dataset[i]
            if len(sample) == 3:  # points, epsr_values, obj_type
                obj_type = sample[2]
            else:  # points, epsr_values, true_labels, obj_type
                obj_type = sample[3]
            obj_types.add(obj_type)
        available_objects = sorted(list(obj_types))

    print(f"找到以下对象类型: {', '.join(available_objects)}")

    # 显示可用对象类型
    print("\n可用的对象类型:")
    for i, obj_type in enumerate(available_objects):
        print(f"{i+1}. {obj_type}")

    try:
        type_choice = int(input("\n请选择对象类型 (输入序号): "))
        if type_choice < 1 or type_choice > len(available_objects):
            print("无效的选择")
            return

        selected_type = available_objects[type_choice-1]

        print(f"处理所选类型 {selected_type} 的所有样本")

        # 直接从文件加载数据，确保能处理所有对象类型
        import h5py

        # 获取选定对象类型的h5文件路径
        h5_file_path = os.path.join(args.data_dir, f"{selected_type}_with_em.h5")

        if not os.path.exists(h5_file_path):
            print(f"错误: 找不到文件 {h5_file_path}")
            return

        print(f"从文件加载数据: {h5_file_path}")

        # 创建一个图表，显示多个样本的结果
        plt.figure(figsize=(15, 12))

        try:
            with h5py.File(h5_file_path, 'r') as f:
                # 检查文件中的数据集
                modes = ['train', 'test']
                processed_samples = 0
                max_samples = 4  # 最多处理4个样本

                for mode in modes:
                    if mode in f:
                        print(f"\n处理 {mode} 数据...")
                        sample_keys = list(f[mode].keys())

                        # 如果样本太多，随机选择一部分
                        import random
                        if len(sample_keys) > max_samples:
                            sample_keys = random.sample(sample_keys, max_samples)

                        for i, sample_key in enumerate(sample_keys):
                            if processed_samples >= max_samples:
                                break

                            print(f"处理样本 {sample_key}...")

                            # 读取点云和EM数据
                            data = f[f'{mode}/{sample_key}/pcs_epsr'][:]

                            # 确保数据不为空且形状正确
                            if data.size == 0 or data.shape[1] != 4:
                                print(f"警告: 样本 {sample_key} 的数据为空或形状不正确，跳过")
                                continue

                            # 分离点云坐标和介电常数
                            points = data[:, :3]
                            epsr_values = data[:, 3]

                            print(f"样本 {sample_key}, 点数: {points.shape[0]}")

                            # 处理点云
                            print("处理点云数据...")
                            material_labels, _ = robot_recognition.process_point_cloud(points, epsr_values)

                            # 分割点云
                            segmented_points = robot_recognition.segment_by_material(points, material_labels)

                            # 创建子图
                            plt.subplot(2, 2, processed_samples + 1, projection='3d')

                            # 获取颜色
                            colors = [list(MATERIAL_PROPERTIES.values())[label]['color'] for label in material_labels]

                            # 绘制点云
                            ax = plt.gca()
                            ax.scatter(points[:, 0], points[:, 1], points[:, 2], c=colors, s=5, alpha=0.8)

                            # 设置标题
                            ax.set_title(f"{selected_type} - 样本 {sample_key}", fontsize=12)

                            # 添加图例
                            if processed_samples == 0:  # 只在第一个子图添加图例
                                legend_elements = [plt.Line2D([0], [0], marker='o', color='w',
                                                            markerfacecolor=props['color'],
                                                            label=props['name'],
                                                            markersize=10)
                                                for props in MATERIAL_PROPERTIES.values()]
                                ax.legend(handles=legend_elements, loc='upper right', fontsize=10)

                            processed_samples += 1

                            # 计算材料分布统计
                            material_counts = {}
                            for material, props in MATERIAL_PROPERTIES.items():
                                count = np.sum(material_labels == props['label'])
                                material_counts[material] = count

                            # 打印材料分布统计 - 先显示介电常数，再显示材料名
                            print(f"材料分布统计 (总点数: {len(material_labels)}):")
                            for material, count in material_counts.items():
                                if count > 0:  # 只显示存在的材料
                                    percentage = (count / len(material_labels)) * 100
                                    props = MATERIAL_PROPERTIES[material]

                                    # 计算该材料的平均介电常数
                                    mask = material_labels == props['label']
                                    avg_epsr = np.mean(epsr_values[mask])
                                    epsr_range = props.get('epsr_range', 'N/A')

                                    # 先显示介电常数，再显示材料名
                                    print(f"  εᵣ={avg_epsr:.2f} ({epsr_range}) - {props['name']}: {count} 点 ({percentage:.1f}%)")

                if processed_samples == 0:
                    print(f"警告: 没有处理任何样本")
                    return

                # 调整布局并保存图像
                plt.tight_layout()
                plt.savefig(f"{selected_type}_材料分布_多样本.png", dpi=300)
                print(f"已保存多样本可视化图像到 {selected_type}_材料分布_多样本.png")
                plt.show()

                # 询问是否保存分割后的点云
                save_choice = input("是否保存最后一个样本的分割点云? (y/n): ")
                if save_choice.lower() == 'y':
                    robot_recognition.save_segmented_point_cloud(segmented_points)
                    print("已保存分割点云")

        except Exception as e:
            print(f"处理数据时出错: {str(e)}")

        print("\n演示完成")

    except (ValueError, IndexError) as e:
        print(f"错误: {str(e)}")

def main():
    args = parse_args()

    if args.mode == 'train':
        train_mode(args)
    elif args.mode == 'test':
        test_mode(args)
    elif args.mode == 'demo':
        demo_mode(args)
    else:
        print(f"错误: 未知模式 {args.mode}")

if __name__ == "__main__":
    main()








