#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证材料显示格式是否正确
先显示介电常数数据，再显示材料名
"""

import numpy as np
import matplotlib.pyplot as plt
from material_distribution_model import MATERIAL_PROPERTIES

def test_display_format():
    """
    测试材料显示格式
    """
    print("=" * 50)
    print("测试材料显示格式")
    print("=" * 50)
    
    # 模拟一些材料数据
    material_labels = np.array([0, 0, 0, 1, 1, 2, 2, 3])  # 木材、塑料、金属、未知
    epsr_values = np.array([3.2, 3.1, 3.3, 2.5, 2.4, 1.5, 1.8, 4.0])
    
    print("\n模拟数据:")
    print(f"材料标签: {material_labels}")
    print(f"介电常数值: {epsr_values}")
    
    # 计算每种材料的统计信息
    material_counts = {}
    for material, props in MATERIAL_PROPERTIES.items():
        count = np.sum(material_labels == props['label'])
        material_counts[material] = count
    
    print("\n材料分布统计 (先显示介电常数，再显示材料名):")
    for material, count in material_counts.items():
        if count > 0:  # 只显示存在的材料
            percentage = (count / len(material_labels)) * 100
            props = MATERIAL_PROPERTIES[material]
            
            # 计算该材料的平均介电常数
            mask = material_labels == props['label']
            avg_epsr = np.mean(epsr_values[mask])
            epsr_range = props.get('epsr_range', 'N/A')
            
            # 先显示介电常数，再显示材料名
            print(f"  εᵣ={avg_epsr:.2f} ({epsr_range}) - {props['name']}: {count} 点 ({percentage:.1f}%)")
    
    print("\n测试完成！")
    print("显示格式：εᵣ=实际值 (理论范围) - 材料名: 点数 (百分比)")

if __name__ == "__main__":
    test_display_format()
