import os
import numpy as np
import torch
import open3d as o3d
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from material_distribution_model import MaterialDistributionModel, MATERIAL_PROPERTIES

class RobotMaterialRecognition:
    def __init__(self, model_path, device=None):
        """
        初始化机械臂材料识别系统

        Args:
            model_path: 模型路径
            device: 计算设备
        """
        # 设置设备
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = device

        print(f"使用设备: {self.device}")

        # 加载模型
        self.model = MaterialDistributionModel(num_materials=len(MATERIAL_PROPERTIES))
        self.model.load_state_dict(torch.load(model_path, map_location=self.device))
        self.model.to(self.device)
        self.model.eval()

        print(f"模型已加载: {model_path}")

    def process_point_cloud(self, points, epsr_values, confidence_threshold=0.6):
        """
        处理点云数据，增强版 - 考虑介电常数值和空间特征

        Args:
            points: 点云坐标，形状为(N, 3)
            epsr_values: 介电常数值，形状为(N,)
            confidence_threshold: 置信度阈值，低于此值的预测将被标记为未知材料

        Returns:
            material_labels: 材料标签，形状为(N,)
            material_probs: 材料概率，形状为(N, num_materials)
        """
        # 转换为张量
        points_tensor = torch.FloatTensor(points).unsqueeze(0).to(self.device)  # (1, N, 3)
        epsr_tensor = torch.FloatTensor(epsr_values).unsqueeze(0).to(self.device)  # (1, N)

        # 预测材料分布
        with torch.no_grad():
            material_logits, attention_weights = self.model(points_tensor, epsr_tensor)

            # 获取材料概率
            material_probs = torch.softmax(material_logits, dim=2)

            # 获取材料标签和置信度
            material_labels = torch.argmax(material_probs, dim=2)
            confidence_values = torch.max(material_probs, dim=2)[0]

            # 将低置信度的预测标记为未知材料
            unknown_label = MATERIAL_PROPERTIES['unknown']['label']
            low_confidence_mask = confidence_values < confidence_threshold
            material_labels[low_confidence_mask] = unknown_label

            # 转换为NumPy数组
            material_labels_np = material_labels[0].cpu().numpy()
            material_probs_np = material_probs[0].cpu().numpy()

            # 打印材料分布统计
            print("\n预测的材料分布:")
            for material, props in MATERIAL_PROPERTIES.items():
                count = np.sum(material_labels_np == props['label'])
                if count > 0:
                    percentage = (count / len(material_labels_np)) * 100
                    print(f"  {props['name']}: {count} 点 ({percentage:.1f}%)")

            # 打印平均置信度
            avg_confidence = confidence_values[0].cpu().numpy().mean()
            print(f"平均预测置信度: {avg_confidence:.4f}")

        return material_labels_np, material_probs_np

    def visualize_material_distribution(self, points, material_labels, title="材料分布", epsr_values=None):
        """
        可视化材料分布

        Args:
            points: 点云坐标，形状为(N, 3)
            material_labels: 材料标签，形状为(N,)
            title: 图表标题
            epsr_values: 介电常数值，形状为(N,)，可选
        """
        # 创建图表
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')

        # 获取颜色
        colors = [list(MATERIAL_PROPERTIES.values())[label]['color'] for label in material_labels]

        # 绘制点云
        ax.scatter(points[:, 0], points[:, 1], points[:, 2], c=colors, s=5)

        # 添加图例
        legend_elements = [plt.Line2D([0], [0], marker='o', color='w',
                                     markerfacecolor=props['color'],
                                     label=props['name'],
                                     markersize=10)
                          for props in MATERIAL_PROPERTIES.values()]
        ax.legend(handles=legend_elements, loc='upper right')

        # 设置标题和轴标签
        ax.set_title(title, fontsize=16)
        ax.set_xlabel('X轴', fontsize=12)
        ax.set_ylabel('Y轴', fontsize=12)
        ax.set_zlabel('Z轴', fontsize=12)

        # 计算材料分布统计
        material_counts = {}
        for material, props in MATERIAL_PROPERTIES.items():
            count = np.sum(material_labels == props['label'])
            material_counts[material] = count

        # 打印材料分布统计 - 先显示介电常数，再显示材料名
        print(f"\n材料分布统计 (总点数: {len(material_labels)}):")
        for material, count in material_counts.items():
            if count > 0:  # 只显示存在的材料
                percentage = (count / len(material_labels)) * 100
                props = MATERIAL_PROPERTIES[material]

                if epsr_values is not None:
                    # 计算该材料的平均介电常数
                    mask = material_labels == props['label']
                    avg_epsr = np.mean(epsr_values[mask])
                    epsr_range = props.get('epsr_range', 'N/A')

                    # 先显示介电常数，再显示材料名
                    print(f"  εᵣ={avg_epsr:.2f} ({epsr_range}) - {props['name']}: {count} 点 ({percentage:.1f}%)")
                else:
                    # 如果没有介电常数数据，只显示材料名
                    print(f"  {props['name']}: {count} 点 ({percentage:.1f}%)")

        plt.tight_layout()
        plt.show()

    def segment_by_material(self, points, material_labels):
        """
        按材料分割点云

        Args:
            points: 点云坐标，形状为(N, 3)
            material_labels: 材料标签，形状为(N,)

        Returns:
            segmented_points: 按材料分割的点云字典
        """
        segmented_points = {}

        for material, props in MATERIAL_PROPERTIES.items():
            mask = material_labels == props['label']
            if np.sum(mask) > 0:
                segmented_points[material] = points[mask]

        return segmented_points

    def save_segmented_point_cloud(self, segmented_points, output_dir="segmented_materials"):
        """
        保存分割后的点云

        Args:
            segmented_points: 按材料分割的点云字典
            output_dir: 输出目录
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 保存分割后的点云
        for material, points in segmented_points.items():
            # 创建点云对象
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)

            # 设置颜色
            color_hex = MATERIAL_PROPERTIES[material]['color']
            if color_hex.startswith('#'):
                color_hex = color_hex[1:]
            # 将十六进制颜色转换为RGB值
            color = np.array([int(color_hex[i:i+2], 16) for i in (0, 2, 4)]) / 255.0
            pcd.colors = o3d.utility.Vector3dVector(np.tile(color, (len(points), 1)))

            # 保存点云
            output_path = os.path.join(output_dir, f"{material}.ply")
            o3d.io.write_point_cloud(output_path, pcd)
            print(f"已保存 {material} 点云到 {output_path}")

    def process_from_file(self, file_path):
        """
        从文件加载点云数据并处理

        Args:
            file_path: 点云文件路径

        Returns:
            material_labels: 材料标签
            segmented_points: 按材料分割的点云
        """
        # 加载点云
        if file_path.endswith('.ply') or file_path.endswith('.pcd'):
            pcd = o3d.io.read_point_cloud(file_path)
            points = np.asarray(pcd.points)

            # 如果没有颜色信息，使用默认的介电常数值
            if not pcd.has_colors():
                print("警告: 点云没有颜色信息，使用默认的介电常数值")
                epsr_values = np.ones(len(points)) * 2.5  # 默认介电常数值
            else:
                # 使用颜色的红色通道作为介电常数值的近似
                colors = np.asarray(pcd.colors)
                epsr_values = colors[:, 0] * 3.5  # 映射到介电常数范围
        else:
            # 尝试加载numpy数组
            try:
                data = np.load(file_path)
                if isinstance(data, dict):
                    points = data.get('points', None)
                    epsr_values = data.get('epsr', None)
                else:
                    # 假设数据是形状为(N, 4)的数组，前3列是坐标，最后一列是介电常数
                    points = data[:, :3]
                    epsr_values = data[:, 3]
            except:
                raise ValueError(f"不支持的文件格式: {file_path}")

        # 处理点云
        material_labels, _ = self.process_point_cloud(points, epsr_values)

        # 分割点云
        segmented_points = self.segment_by_material(points, material_labels)

        # 可视化材料分布
        self.visualize_material_distribution(points, material_labels, title=f"材料分布 - {os.path.basename(file_path)}", epsr_values=epsr_values)

        return material_labels, segmented_points

def simulate_robot_scan():
    """
    模拟机械臂扫描，生成带有介电常数的点云数据

    Returns:
        points: 点云坐标
        epsr_values: 介电常数值
    """
    # 创建一个简单的物体模型（例如，一个有不同材料的桌子）
    # 桌面 - 木材
    table_top_points = np.random.rand(1000, 3)
    table_top_points[:, 0] *= 1.0
    table_top_points[:, 1] *= 0.6
    table_top_points[:, 2] = 0.7
    table_top_epsr = np.random.uniform(2.8, 3.5, 1000)  # 木材的介电常数范围

    # 桌腿 - 金属
    table_legs = []
    leg_positions = [(0, 0), (0, 0.6), (1.0, 0), (1.0, 0.6)]

    for x, y in leg_positions:
        leg_points = np.random.rand(250, 3)
        leg_points[:, 0] = x + leg_points[:, 0] * 0.05
        leg_points[:, 1] = y + leg_points[:, 1] * 0.05
        leg_points[:, 2] *= 0.7
        table_legs.append(leg_points)

    table_legs_points = np.vstack(table_legs)
    table_legs_epsr = np.random.uniform(1.0, 2.0, len(table_legs_points))  # 金属的介电常数范围

    # 桌上物品 - 塑料
    object_points = np.random.rand(500, 3)
    object_points[:, 0] = 0.3 + object_points[:, 0] * 0.2
    object_points[:, 1] = 0.2 + object_points[:, 1] * 0.2
    object_points[:, 2] = 0.7 + object_points[:, 2] * 0.2
    object_epsr = np.random.uniform(2.0, 2.8, 500)  # 塑料的介电常数范围

    # 合并所有点
    points = np.vstack([table_top_points, table_legs_points, object_points])
    epsr_values = np.concatenate([table_top_epsr, table_legs_epsr, object_epsr])

    return points, epsr_values

def main():
    # 检查模型文件是否存在
    model_path = "material_model_final.pth"
    if not os.path.exists(model_path):
        print(f"错误: 模型文件 {model_path} 不存在")
        print("请先运行 train_material_model.py 训练模型")
        return

    # 初始化机械臂材料识别系统
    robot_recognition = RobotMaterialRecognition(model_path)

    # 模拟机械臂扫描
    print("模拟机械臂扫描...")
    points, epsr_values = simulate_robot_scan()

    # 处理点云
    print("处理点云数据...")
    material_labels, _ = robot_recognition.process_point_cloud(points, epsr_values)

    # 分割点云
    segmented_points = robot_recognition.segment_by_material(points, material_labels)

    # 保存分割后的点云
    robot_recognition.save_segmented_point_cloud(segmented_points)

    # 可视化材料分布
    robot_recognition.visualize_material_distribution(points, material_labels, title="机械臂扫描 - 材料分布", epsr_values=epsr_values)

if __name__ == "__main__":
    main()
